package com.beantechs.bigdata.service.config;


import com.beantechs.bigdata.service.handle.AsyncExecuteThreadPoolHandle;
import com.beantechs.bigdata.service.handle.CalculateHandle;
import com.beantechs.service.utils.CommonBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SchedulerCalculateTask {

    private final CalculateHandle calculateHandle;

    private final KafkaConsumer kafkaConsumer;

    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    public SchedulerCalculateTask(CalculateHandle calculateHandle, KafkaConsumer kafkaConsumer, AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle) {
        this.calculateHandle = calculateHandle;
        this.kafkaConsumer = kafkaConsumer;
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
    }

    @Scheduled(cron = "${scheduler.handle.time.cron}")
    @PostConstruct
    public void handle() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("query request...");
        if (Boolean.TRUE.equals(calculateHandle.queryAndCalculateScenario())) {
            log.warn("calculate scenario successful! current time:{}", CommonBeanUtils.dateTime2String(new Date()));
        }
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
    }


    /**
     * 定时刷库计算信用分
     */
    @Scheduled(cron = "${scheduler.handle.time.cron}")
    public void schedulerScenarioTreeScores() {
        try {
            kafkaConsumer.refreshScores();
        } catch (Exception e) {
            log.error("查询MySQL剧情树信息失败, 刷新缓存失败!");
            asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("查询MySQL剧情树信息失败, 刷新缓存失败!", e.getMessage());
        }
    }


    /**
     * 同步标签、服务关系
     */
    @Scheduled(cron = "${scheduler.handle.time.cron}")
    public void syncScenarioTagInfo() {
        try {
            asyncExecuteThreadPoolHandle.syncScenarioTagInfo();
        } catch (Exception e) {
            log.error("同步标签服务信息失败!");
            asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("同步标签服务信息失败!", e.getMessage());
        }
    }


    @Scheduled(cron = "${scheduler.handle.time.cron}")
    public void syncScenarioLevel() {
        try {
            asyncExecuteThreadPoolHandle.syncScenarioLevel();
        } catch (Exception e) {
            log.error("同步剧本等级信息失败!");
            asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("同步剧本等级信息失败!", e.getMessage());
        }
    }


}
