package com.beantechs.bigdata.service.entity.business;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioInfoTreeBusiness implements Serializable {

    /**
     * 剧本id
     */
    private Integer scenarioId;
    /**
     * 剧本code
     */
    private String scenarioCode;
    /**
     * 剧本code
     */
    private String scenarioName;
    /**
     * 触发器类型
     */
    private String triggerTypes;
    /**
     * 触发器数量
     */
    private Integer triggerNum;
    /**
     * 剧本状态
     */
    private Integer scenarioState;
    /**
     * 发布日期
     */
    private Date publishDate;
    /**
     * 生效日期
     */
    private Date validityStartDate;
    /**
     * 失效日期
     */
    private Date validityEndDate;
    /**
     * 竞价金额
     */
    private double calAmount;
    /**
     * 剧本单价
     */
    private BigDecimal scenarioPrice;
    /**
     * 剧情树名称
     */
    private String treeName;
    /**
     * 剧情树Code
     */
    private String treeCode;

}
