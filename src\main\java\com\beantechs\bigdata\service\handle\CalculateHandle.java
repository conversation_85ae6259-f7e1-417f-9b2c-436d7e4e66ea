package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioInfo;
import com.beantechs.bigdata.service.entity.ScenarioPkInfo;
import com.beantechs.bigdata.service.entity.ScenarioRelationTree;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import com.beantechs.bigdata.service.mapper.ScenarioBigdataMapper;
import com.beantechs.bigdata.service.mapper.ScenarioBigdataSlaveMapper;
import com.beantechs.bigdata.service.mapper.ScenarioBusinessMapper;
import com.beantechs.bigdata.service.mapper.ScenarioRelationTreeMapper;
import com.beantechs.service.exception.DIYException;
import com.beantechs.service.utils.CommonBeanUtils;
import com.beantechs.service.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CalculateHandle {

    @Resource
    private ScenarioBusinessMapper scenarioBusinessMapper;

    @Resource
    private ScenarioBigdataMapper scenarioBigdataMapper;


    @Value("${day.credit.weight}")
    private BigDecimal dayCreditWeight;

    @Value("${score.credit.weight}")
    private BigDecimal scoreCreditWeight;

    @Value("${scenario.price.updated.weight.multiply}")
    private BigDecimal scenarioPriceUpdatedWeightMultiply;

    @Value("${scenario.price.updated.weight.add}")
    private BigDecimal scenarioPriceUpdatedWeightAdd;

    @Value("${normal.weight}")
    private BigDecimal normalWeight;

    @Value("${normal.price.weight}")
    private BigDecimal normalPriceWeight;

    @Value("${redis.key.without.trigger.pk.scores.prefix}")
    private String pkScenarioScorePrefixKey;

    private final ScenarioBigdataSlaveMapper scenarioBigdataSlaveMapper;
    private final ScenarioRelationTreeMapper scenarioRelationTreeMapper;

    /**
     * 异步处理对象
     */
    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    private final JedisPool jedisPool;

    /**
     * redis库:8
     */
    @Value("${redis.database}")
    private Integer indexDB;

    @Value("${redis.key.pk.scores.prefix}")
    private String pkScorePrefixKey;

    @Value("${redis.key.lock}")
    private String lock;


    public CalculateHandle(
            ScenarioRelationTreeMapper scenarioRelationTreeMapper,
            AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle,
            @Qualifier("jedisPool") JedisPool jedisPool,
            ScenarioBigdataSlaveMapper scenarioBigdataSlaveMapper
    ) {
        this.scenarioRelationTreeMapper = scenarioRelationTreeMapper;
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
        this.jedisPool = jedisPool;
        this.scenarioBigdataSlaveMapper = scenarioBigdataSlaveMapper;
    }

    private static void resetTriggerType(Scenario s) {
        if ("null-triggerType".equals(s.getTriggerType())) {
            s.setTriggerType("");
        }
    }


    /**
     * 多数据源，加方法锁，同一时刻只有一个线程可以访问该方法
     */
    public Boolean queryAndCalculateScenario() {
        //设置分布式锁
        if (sentnx()) return false;

        //查询剧本信息，构建剧本对象
        List<ScenarioInfo> scenarioInfos = queryAndBuildScenarioObject();

//        List<ScenarioInfo> scenarioSet = scenarioInfos.stream().filter(item -> StringUtils.isBlank(item.getTreeCode())).collect(Collectors.toList());
        List<ScenarioInfo> treeSet = scenarioInfos.stream().filter(item -> StringUtils.isNotBlank(item.getTreeCode())).collect(Collectors.toList());
        List<ScenarioInfo> scenarioSetNew = new ArrayList<>(scenarioInfos);

        //计算剧本分数，并构建Pk基础信用分对象
        List<ScenarioPkInfo> scenarioPkInfoList = calScenarioScores(scenarioSetNew);
        List<ScenarioPkInfo> treePkInfoList = calScenarioScores(treeSet);

        //保存剧本基本信息
        final Date date = new Date();
        if (!CollectionUtils.isEmpty(scenarioPkInfoList)) {
            //通过scenarioPkInfoList对触发器进行拆解
            List<Scenario> scenarioList = getScenarioTriggerInfoList(scenarioSetNew, scenarioPkInfoList);

            //根据scenarioCode过滤去重, 忽略treeCode和treeName
            List<ScenarioPkInfo> pkDistinctByScenarioCode = scenarioPkInfoList.stream().filter(distinctByKey(ScenarioPkInfo::getScenarioCode)).collect(Collectors.toList());

            //根据scenarioCode和triggerType过滤去重, 忽略treeCode和treeName
            List<Scenario> triggerDistinctByScenarioCodeWithTrigger = scenarioList.stream()
                    .filter(distinctByKey(Scenario::getScenarioCode, Scenario::getTriggerType))
                    .peek(CalculateHandle::resetTriggerType)
                    .collect(Collectors.toList());

            save2MySQL(triggerDistinctByScenarioCodeWithTrigger, pkDistinctByScenarioCode, null, date);

            //移除redis剧本缓存信息，并重新写入
            removeRedisOldKey("scenario");
            redisHandle(triggerDistinctByScenarioCodeWithTrigger, null, date);
        }

        if (!CollectionUtils.isEmpty(treePkInfoList)) {
            //拼装剧本剧情树关系
            List<ScenarioPkInfo> treeCollect = treePkInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getTreeCode())).collect(Collectors.toList());
            List<ScenarioRelationTree> scenarioRelationTreeList = treeCollect.stream().map(ScenarioRelationTree::of).distinct().collect(Collectors.toList());

            save2MySQL(null, null, scenarioRelationTreeList, date);

            //移除redis剧本缓存信息，并重新写入
            removeRedisOldKey("tree");
            redisHandle(null, treeCollect, date);
        }

        //释放锁
        RedisUtils.del(jedisPool, indexDB, lock);
        return true;
    }


    @SafeVarargs
    private static <T> java.util.function.Predicate<T> distinctByKey(Function<? super T, ?>... keyExtractors) {
        final Map<List<?>, Boolean> seen = new ConcurrentHashMap<>();
        return t -> {
            final List<?> keys = Arrays.stream(keyExtractors).map(ke -> ke.apply(t)).collect(Collectors.toList());
            return seen.putIfAbsent(keys, Boolean.TRUE) == null;
        };
    }

    /**
     * 查询并构建剧本对象
     */
    @NotNull
    private List<ScenarioInfo> queryAndBuildScenarioObject() {
        //查询剧本
        List<ScenarioInfo> collect = scenarioBusinessMapper.queryScenarioInfos().stream().map(item -> {
            ScenarioInfo scenarioInfo = new ScenarioInfo();
            BeanUtils.copyProperties(item, scenarioInfo);
            return scenarioInfo;
        }).collect(Collectors.toList());

        //设置触发器数量
        List<ScenarioInfo> scenarioInfos = new ArrayList<>();

        Set<String> scenarioCodeSet = collect.stream().map(ScenarioInfo::getScenarioCode).collect(Collectors.toSet());
        List<ScenarioTagInfo> scenarioTagInfoList = scenarioBigdataSlaveMapper.queryTagByScenarioCode(scenarioCodeSet);
        Map<String, List<String>> scenarioTagMap = scenarioTagInfoList.stream()
                .filter(info -> info.getTagCode() != null && !info.getTagCode().isEmpty())
                .collect(Collectors.groupingBy(
                        ScenarioTagInfo::getScenarioCode,
                        Collectors.mapping(ScenarioTagInfo::getTagCode, Collectors.toList())
                ));

        for (ScenarioInfo scenarioInfo : collect) {
            if (StringUtils.isNotBlank(scenarioInfo.getTriggerTypes())) {
                scenarioInfo.setTriggerNum(scenarioInfo.getTriggerTypes().split(ScenarioConstants.JAVA_COMMON_SPLIT_STR).length);
                scenarioInfos.add(scenarioInfo);
            } else {
                if (scenarioTagMap.containsKey(scenarioInfo.getScenarioCode())) {
                    List<String> tags = scenarioTagMap.get(scenarioInfo.getScenarioCode());
                    scenarioInfo.setTriggerTypes(StringUtils.join(tags, ScenarioConstants.JAVA_COMMON_SPLIT_STR));
                    scenarioInfo.setTriggerNum(tags.size());
                    scenarioInfos.add(scenarioInfo);
                } else {
                    scenarioInfo.setTriggerTypes("");
                    scenarioInfo.setTriggerNum(0);
                    scenarioInfos.add(scenarioInfo);
                }
            }
        }

        return scenarioInfos;
    }


    public List<ScenarioPkInfo> calScenario(List<ScenarioInfo> scenarioInfos) {
        return calScenarioScores(scenarioInfos);
    }

    public List<Scenario> calScenarioTriggerInfoList(List<ScenarioInfo> scenarioInfos, List<ScenarioPkInfo> scenarioPkInfoList) {
        return getScenarioTriggerInfoList(scenarioInfos, scenarioPkInfoList);
    }


    /**
     * @param maxAmount          所有剧本中最大的剧本金额
     * @param currentDateTimeStr 当天时间字符串
     * @param scenarioInfo       剧本基础信息
     * @return 计算后的剧本竞价信息
     */
    private ScenarioPkInfo calScenarioScores(Double maxAmount, String currentDateTimeStr, ScenarioInfo scenarioInfo) {

        ScenarioPkInfo scenarioPkInfo = ScenarioPkInfo.of(scenarioInfo, dayCreditWeight, scoreCreditWeight, scenarioPriceUpdatedWeightMultiply, scenarioPriceUpdatedWeightAdd, normalPriceWeight, normalWeight, currentDateTimeStr, maxAmount);
        final BigDecimal scenarioAvgValue = scenarioPkInfo.getScenarioTotalCredit();
        scenarioPkInfo.setScenarioTotalWithAvgCredit(scenarioAvgValue);
        scenarioPkInfo.setScenarioDestWithAvgCredit(normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(scenarioAvgValue).setScale(3, RoundingMode.HALF_UP));
        scenarioPkInfo.setId(scenarioInfo.getVin() + "_" + scenarioInfo.getTreeCode() + "_" + scenarioInfo.getScenarioCode());
        scenarioPkInfo.setVin(scenarioInfo.getVin());
        scenarioPkInfo.setTreeCode(StringUtils.isNotBlank(scenarioInfo.getTreeCode()) ? scenarioInfo.getTreeCode() : null);
        scenarioPkInfo.setTreeName(StringUtils.isNotBlank(scenarioInfo.getTreeName()) ? scenarioInfo.getTreeName() : null);
        return scenarioPkInfo;
    }


    /**
     * @param triggerDistinctByScenarioCodeWithTrigger 剧本根据触发器分类信用分列表
     * @param treeCollect                              剧情树不根据触发器分类信用分列表
     * @param date                                     日期
     */
    private void redisHandle(List<Scenario> triggerDistinctByScenarioCodeWithTrigger, List<ScenarioPkInfo> treeCollect, Date date) {
        //************************************************剧本根据触发器分类, 一个触发器一条*******************************************************//
        //根据剧本code分组
        if (!CollectionUtils.isEmpty(triggerDistinctByScenarioCodeWithTrigger)) {
            final Map<String, List<Scenario>> scenarioCollect = triggerDistinctByScenarioCodeWithTrigger.stream().collect(Collectors.groupingBy(Scenario::getScenarioCode));

            //组建剧本为key的Pk对象放入缓存待用
            List<String> saveScenarioTriggerList = new ArrayList<>();
            scenarioCollect.keySet().forEach(code -> extractedFunctionScenario(saveScenarioTriggerList, scenarioCollect, code));
            String scenarioSet = RedisUtils.mset(jedisPool, indexDB, saveScenarioTriggerList.toArray(new String[0]));
            if (!"OK".equals(scenarioSet)) {
                log.error("保存剧本信息至redis失败!");
                asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("保存剧本计算信息至redis失败", "无");
            }
            log.warn("保存剧本信息至redis成功,当前时间:{}", CommonBeanUtils.dateTime2String(date));
            log.warn("保存redis设置值: 剧本key：{}", JSON.toJSONString(saveScenarioTriggerList));
            log.warn("保存到redis的PK信息：【{}】", JSON.toJSONString(scenarioCollect));
        }


        //************************************************剧情树不根据触发器分类, 一个剧情树一条*******************************************************//
        if (!CollectionUtils.isEmpty(treeCollect)) {
            //组建剧情树为key的Pk对象放入缓存待用
            List<String> saveScenarioList = new ArrayList<>();
            treeCollect.forEach(item -> {
                String key = pkScenarioScorePrefixKey + item.getTreeCode();
                String value = JSON.toJSONString(item);
                saveScenarioList.add(key);
                saveScenarioList.add(value);
            });
            String treeSet = RedisUtils.mset(jedisPool, indexDB, saveScenarioList.toArray(new String[0]));
            if (!"OK".equals(treeSet)) {
                log.error("保存不包含触发器剧情树计算信息至redis失败!, treeSet:{}", treeSet);
                asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("保存不包含触发器剧情树计算信息至redis失败", "无");
            }
            log.warn("保存不包含触发器剧情树计算信息至redis成功,当前时间:{}", CommonBeanUtils.dateTime2String(date));
            log.warn("保存redis设置值: 剧情树key：{}", JSON.toJSONString(saveScenarioList));
        }

    }


    private void extractedFunctionScenario(List<String> list, Map<String, List<Scenario>> collect, String code) {
        List<Scenario> listByCode = collect.get(code);
        String key = pkScorePrefixKey + code;
        String value = JSON.toJSONString(listByCode);
        list.add(key);
        list.add(value);
    }


    /**
     * 计算所有剧本分数
     *
     * @param scenarioInfos poll的剧本信息
     * @return 剧本竞价信息
     */
    @Nullable
    private List<ScenarioPkInfo> calScenarioScores(List<ScenarioInfo> scenarioInfos) {
        if (CollectionUtils.isEmpty(scenarioInfos)) {
            log.warn("query scenario infos failed!");
            return null;
        }
        final Double maxAmount = scenarioInfos.stream().map(ScenarioInfo::getCalAmount).max(Double::compareTo).orElse(0.00);

        //计算剧本P值
        final String currentDateTimeStr = CommonBeanUtils.date2String(new Date()) + " 00:00:00";
        final Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DAY_OF_MONTH, -1);

        //拿到剧本信用值
        List<ScenarioPkInfo> scenarioPkInfoList = new ArrayList<>();
        scenarioInfos.forEach(o -> {
            ScenarioPkInfo scenarioPkInfo = calScenarioScores(maxAmount, currentDateTimeStr, o);
            scenarioPkInfoList.add(scenarioPkInfo);
        });
        return scenarioPkInfoList;
    }

    /**
     * 设置分布式锁避免多个实例进行poll操作
     *
     * @return 是否成功获取锁
     */
    private boolean sentnx() {
        if (RedisUtils.setnx(lock, "1", indexDB, jedisPool) == 1L) {
            RedisUtils.expire(lock, 10, indexDB, jedisPool);
            log.warn("redis设置锁成功,设置过期时间成功,开始计算和保存...");
        } else if (RedisUtils.setnx(lock, "1", indexDB, jedisPool) == 0L) {
            log.warn("redis设置锁失败,重设过期时间...");
            RedisUtils.expire(lock, 10, indexDB, jedisPool);
            log.warn("redis重设过期时间成功,结束任务");
            return true;
        }
        return false;
    }

    /**
     * 保存竞价信息到MySQL
     *
     * @param scenarioPkInfoList 竞价基础信息
     * @param scenarioList       根据触发器分类的竞价信息
     * @param date               日期
     */
    private void save2MySQL(List<Scenario> scenarioList, List<ScenarioPkInfo> scenarioPkInfoList, List<ScenarioRelationTree> scenarioRelationTreeList, Date date) {
        try {
            int BATCH_SIZE = 500;

            if (!CollectionUtils.isEmpty(scenarioPkInfoList)) {
                // Process scenarioPkInfoList in batches
                for (int i = 0; i < scenarioPkInfoList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioPkInfoList.size());
                    List<ScenarioPkInfo> batchList = scenarioPkInfoList.subList(i, end);
                    scenarioBigdataMapper.saveScenarioPkInfos(batchList, date);
                    log.warn("Saved ScenarioPkInfos batch from index {} to {}", i, end - 1);
                }
            }

            if (!CollectionUtils.isEmpty(scenarioList)) {
                // Process scenarioList in batches
                for (int i = 0; i < scenarioList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioList.size());
                    List<Scenario> batchList = scenarioList.subList(i, end);
                    scenarioBigdataMapper.saveScenarioTriggerTypeScore(batchList, date);
                    log.warn("Saved ScenarioTriggerTypeScore batch from index {} to {}", i, end - 1);
                }
            }

            if (!CollectionUtils.isEmpty(scenarioRelationTreeList)) {
                // Process scenarioRelationTreeList in batches
                for (int i = 0; i < scenarioRelationTreeList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioRelationTreeList.size());
                    List<ScenarioRelationTree> batchList = scenarioRelationTreeList.subList(i, end);
                    scenarioRelationTreeMapper.saveScenarioRelationTreeBatch(batchList);
                    log.warn("Saved ScenarioRelationTree batch from index {} to {}", i, end - 1);
                }
            }
        } catch (Exception e) {
            log.error("保存剧本pk信用分信息到MySQL失败,错误信息:{}", e.getMessage());
            asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("保存剧本pk信用分信息到MySQL失败", e.getMessage());
            throw new DIYException("保存剧本pk信用分信息到MySQL失败!", e.getCause());
        }
    }

    private void removeRedisOldKey(String type) {
        switch (type) {
            case "scenario":
                log.info("删除redis剧本对象旧值");
                Set<String> delScenarioKeys = RedisUtils.scan(pkScorePrefixKey + ScenarioConstants.JAVA_COMMON_STAR_STR, indexDB, jedisPool, 1000);
                Long scenarioDelNum = RedisUtils.del(jedisPool, indexDB, delScenarioKeys.toArray(new String[0]));
                log.info("保存剧本触发器对应信用分到redis, 删除的旧key:{}, 删除key数量:{}", JSON.toJSONString(delScenarioKeys), scenarioDelNum);
                break;
            case "tree":
                log.info("删除redis剧情树对象旧值");
                Set<String> delTreeKeys = RedisUtils.scan(pkScenarioScorePrefixKey + ScenarioConstants.JAVA_COMMON_STAR_STR, indexDB, jedisPool, 1000);
                Long treeDelNum = RedisUtils.del(jedisPool, indexDB, delTreeKeys.toArray(new String[0]));
                log.info("保存剧本触发器对应信用分到redis, 删除的旧key:{}, 删除key数量:{}", JSON.toJSONString(delTreeKeys), treeDelNum);
                break;
        }
    }

    /**
     * 打散剧本基本信息，根据触发器、剧本code分类
     *
     * @param scenarioInfos      剧本信息
     * @param scenarioPkInfoList 计算获得得剧本竞价对象
     */
    private static List<Scenario> getScenarioTriggerInfoList(List<ScenarioInfo> scenarioInfos, List<ScenarioPkInfo> scenarioPkInfoList) {
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioInfos.forEach(obj -> processTriggerTypes(obj, scenarioPkInfoList, scenarioList));
        return scenarioList;
    }

    private static void processTriggerTypes(ScenarioInfo obj, List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) {
        if (StringUtils.isBlank(obj.getTriggerTypes())) {
            Scenario scenario = Scenario.of(obj, "null-triggerType", scenarioPkInfoList);
            scenario.setId(obj.getVin() + "_" + obj.getTreeCode() + "_" + obj.getScenarioCode());
            scenario.setVin(obj.getVin());
            scenario.setTreeCode(obj.getTreeCode());
            scenario.setTreeName(obj.getTreeName());
            scenarioList.add(scenario);
        } else {
            Arrays.stream(obj.getTriggerTypes().split(ScenarioConstants.JAVA_COMMON_SPLIT_STR))
                    .forEach(value -> createAndAddScenario(obj, value, scenarioPkInfoList, scenarioList));
        }
    }

    private static void createAndAddScenario(ScenarioInfo obj, String value, List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) {
        Scenario scenario = Scenario.of(obj, value, scenarioPkInfoList);
        scenario.setId(obj.getVin() + "_" + obj.getTreeCode() + "_" + obj.getScenarioCode() + "_" + value);
        scenario.setVin(obj.getVin());
        scenario.setTreeCode(obj.getTreeCode());
        scenario.setTreeName(obj.getTreeName());
        scenarioList.add(scenario);
    }

}
