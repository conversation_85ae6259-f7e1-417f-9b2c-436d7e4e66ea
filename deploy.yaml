apiVersion: K8S_API_VERSION
kind: Deployment
metadata:
  name: smart-audio-scheduler-service
  namespace: default
spec:
  replicas: REPLICASNUM
  strategy:
    rollingUpdate:
      maxSurge: 25%       # 一次可以添加多少个Pod，最少1个
      maxUnavailable: 25% # 滚动更新期间最大多少个Pod不可用，最少0个
  selector:
    matchLabels:
      k8s-app: smart-audio-scheduler-service
  template:
    metadata:
      labels:
        k8s-app: smart-audio-scheduler-service
    spec:
      containers:
        - name: smart-audio-scheduler-service
          image: DOCKER_IMAGE
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: ACTIVE_PROFILE
            - name: JAVA_OPTS
              value: "-XX:+UseContainerSupport"
            - name: aliyun_logs_smart-audio-scheduler-service
              value: stdout
          # 存活检查：kubernetes认为该pod是存活的,不存活则需要重启
          livenessProbe:
            tcpSocket:
              port: 8080
            # equals to the maximum startup time of the application + couple of seconds
            initialDelaySeconds: 900
            periodSeconds: 30
          resources:
            requests:
              cpu: RESOURCES_CPU
              memory: RESOURCES_MEMORY
            limits:
              cpu: RESOURCES_LIMIT_CPU
              memory: RESOURCES_LIMIT_MEMORY
