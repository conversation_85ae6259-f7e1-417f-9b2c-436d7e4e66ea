package com.beantechs.bigdata.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("scenario_relation_scenario_tree_df")
public class ScenarioRelationTree {

    @TableId("id")
    private String id;

    @TableField("scenario_name")
    private String scenarioName;

    @TableField("scenario_code")
    private String scenarioCode;

    @TableField("tree_name")
    private String treeName;

    @TableField("tree_code")
    private String treeCode;


    public static ScenarioRelationTree of(ScenarioPkInfo item) {
        return new ScenarioRelationTree() {{
            setId(item.getScenarioCode() + "_" + item.getTreeCode());
            setScenarioName(item.getScenarioName());
            setScenarioCode(item.getScenarioCode());
            setTreeName(item.getTreeName());
            setTreeCode(item.getTreeCode());
        }};
    }

}