package com.beantechs.bigdata.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.beantechs.service.utils.CommonBeanUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Data
@TableName("scenario_tree_trigger_type_score_di")
public class Scenario implements Serializable {

    @TableId
    private String id;

    @TableField("vin")
    private String vin;

    @TableField("tree_code")
    private String treeCode;

    @TableField("tree_name")
    private String treeName;

    /**
     * 剧本id
     */
    @TableField("scenario_id")
    private Integer scenarioId;
    /**
     * 剧本code
     */
    @TableField("scenario_code")
    private String scenarioCode;
    /**
     * 剧本name
     */
    @TableField("scenario_name")
    private String scenarioName;
    /**
     * 触发器类型
     */
    @TableField("trigger_type")
    private String triggerType;
    /**
     * 触发器数量
     */
    @TableField("trigger_num")
    private Integer triggerNum;
    /**
     * 反馈值
     */
    @TableField(exist = false)
    private BigDecimal feedbackCredit;
    /**
     * 剧本信用值
     */
    @TableField("scenario_total_credit")
    private BigDecimal scenarioTotalCredit;
    /**
     * 剧本最终信用值，根据正态分布取随机
     */
    @TableField("scenario_dest_credit")
    private BigDecimal scenarioDestCredit;
    /**
     * 剧本信用值-加上均值
     */
    @TableField("scenario_total_with_avg_credit")
    private BigDecimal scenarioTotalWithAvgCredit;
    /**
     * 剧本最终信用值，根据正态分布取随机
     */
    @TableField("scenario_dest_with_avg_credit")
    private BigDecimal scenarioDestWithAvgCredit;

    @TableField("cal_time")
    private String calTime = CommonBeanUtils.dateTime2String(new Date());

    public static Scenario of(ScenarioInfo scenarioInfo, String currentSplit, List<ScenarioPkInfo> scenarioPkInfoList) {
        Scenario scenario = new Scenario();
        scenario.setTriggerType(currentSplit);
        scenario.setScenarioId(scenarioInfo.getScenarioId());
        scenario.setScenarioCode(scenarioInfo.getScenarioCode());
        scenario.setScenarioName(scenarioInfo.getScenarioName());
        scenario.setTriggerNum(scenarioInfo.getTriggerNum());
        final ScenarioPkInfo scenarioPKInfo = scenarioPkInfoList.stream().filter(t -> scenarioInfo.getScenarioCode().equals(t.getScenarioCode())).collect(Collectors.toList()).get(0);
        scenario.setScenarioTotalCredit(scenarioPKInfo.getScenarioTotalCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioDestCredit(scenarioPKInfo.getScenarioDestCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioTotalWithAvgCredit(scenarioPKInfo.getScenarioTotalWithAvgCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioDestWithAvgCredit(scenarioPKInfo.getScenarioDestWithAvgCredit().setScale(3, RoundingMode.HALF_UP));
        return scenario;
    }
}
