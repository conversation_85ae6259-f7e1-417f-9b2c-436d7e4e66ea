package com.beantechs.bigdata.service.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioTagInfoDataserviceMapper extends BaseMapper<ScenarioTagInfo> {


    /**
     * 批量替换场景标签信息
     * 
     * @param scenarioTagInfoList 场景标签信息列表
     * @return 替换的记录数
     */
    @Insert("<script>" +
            "REPLACE INTO scenario_tag_info (id, scenario_code, tag_code, create_by, create_time, update_by, update_time, is_delete) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.scenarioCode}, #{item.tagCode}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.isDelete})" +
            "</foreach>" +
            "</script>")
    Integer replaceBatch(@Param("list") List<ScenarioTagInfo> scenarioTagInfoList);

}
