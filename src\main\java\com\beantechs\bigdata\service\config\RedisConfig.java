package com.beantechs.bigdata.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisConfig {
    @Value("${redis.host}")
    private String redisHost;

    @Value("${redis.port}")
    private Integer redisPort;

    @Value("${redis.maxIdle}")
    private Integer redisMaxIdle;

    @Value("${redis.maxWaitMillis}")
    private Integer redisMaxWaitMillis;

    @Value("${redis.blockWhenExhausted}")
    private boolean redisBlockWhenExhausted;

    @Value("${redis.timeout}")
    private Integer redisTimeOut;

    @Value("${redis.password}")
    private String redisPwd;

    @Bean
    @Qualifier("jedisPool")
    public JedisPool redisPoolFactory() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(redisMaxIdle);
        jedisPoolConfig.setMaxWaitMillis(redisMaxWaitMillis);
        // 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
        jedisPoolConfig.setBlockWhenExhausted(redisBlockWhenExhausted);
        // 是否启用pool的jmx管理功能, 默认true
        jedisPoolConfig.setJmxEnabled(true);
        return new JedisPool(jedisPoolConfig, redisHost, redisPort, redisTimeOut, redisPwd);
    }
}
