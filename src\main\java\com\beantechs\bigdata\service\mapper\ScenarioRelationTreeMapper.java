package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beantechs.bigdata.service.entity.ScenarioRelationTree;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioRelationTreeMapper extends BaseMapper<ScenarioRelationTree> {

    /**
     * 批量保存场景与场景树的关联关系
     * 
     * @param scenarioRelationTreeList 场景关联树信息列表
     * @return 保存的记录数
     */
    @Insert({"<script>",
            "REPLACE INTO scenario_relation_scenario_tree_df ",
            "(id, scenario_name, scenario_code, tree_name, tree_code) VALUES ",
            "<foreach item='item' collection='list' separator=',' index='index'>",
            "(#{item.id}, #{item.scenarioName}, #{item.scenarioCode}, #{item.treeName}, #{item.treeCode})",
            "</foreach>",
            "</script>"
    })
    @DS("dataservice")
    public Integer saveScenarioRelationTreeBatch(@Param("list") List<ScenarioRelationTree> scenarioRelationTreeList);

}