package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.beantechs.bigdata.service.entity.business.ScenarioInfoBusiness;
import com.beantechs.bigdata.service.entity.business.ScenarioInfoTreeBusiness;
import com.beantechs.bigdata.service.entity.business.ScenarioLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("business")
public interface ScenarioBusinessMapper {

    /**
     * 查询所有场景信息
     * 
     * @return 场景业务信息列表
     */
    @Select({"<script>",
            "SELECT scenarioInfo.*,\n" +
            "       tree.tree_name AS treeName,\n" +
            "       tree.tree_code AS treeCode\n" +
            "FROM (SELECT b.tree_code,\n" +
            "             b.scenario_code,\n" +
            "             b.tree_name\n" +
            "      FROM scenario_info a\n" +
            "               INNER JOIN scenario_tree b ON a.scenario_code = b.scenario_code) tree\n" +
            "         RIGHT JOIN (SELECT a.id                       AS scenarioId,\n" +
            "                            a.scenario_code            AS scenarioCode,\n" +
            "                            a.scenario_name            AS scenarioName,\n" +
            "                            a.trigger_types            AS triggerTypes,\n" +
            "                            a.state                    AS scenarioState,\n" +
            "                            IFNULL(\n" +
            "                                    a.publish_time,\n" +
            "                                    DATE_SUB(NOW(), INTERVAL 90 DAY)\n" +
            "                            )                          AS publishDate,\n" +
            "                            IFNULL(\n" +
            "                                    a.validity_start_date,\n" +
            "                                    DATE_SUB(NOW(), INTERVAL 90 DAY)\n" +
            "                            )                          AS validityStartDate,\n" +
            "                            a.validity_end_date        AS validityEndDate,\n" +
            "                            a.version                  AS version,\n" +
            "                            IFNULL(b.cal_amount, 0.00) AS calAmount,\n" +
            "                            IFNULL(b.price, 0.00)      AS scenarioPrice,\n" +
            "                            a.category                 AS category\n" +
            "                     FROM scenario_info a\n" +
            "                              LEFT JOIN scenario_amount b ON a.scenario_code = b.scenario_code\n" +
            "                     WHERE a.state IN (1, 2)\n" +
            "                       AND a.category IN (0, 7)) scenarioInfo ON scenarioInfo.scenarioCode = tree.scenario_code",
            "</script>"
    })
    @DS("business")
    public List<ScenarioInfoBusiness> queryScenarioInfos();

    /**
     * 批量查询场景树信息
     * 
     * @param scenarioTreeCodes1 场景树代码列表1
     * @param scenarioTreeCodes2 场景树代码列表2
     * @return 场景树业务信息列表
     */
    @Select({
            "<script>" +
                    "select scenarioInfo.*, tree_name treeName, tree_code treeCode\n" +
                    "from (select b.tree_code, b.scenario_code, b.tree_name\n" +
                    "      from scenario_info a\n" +
                    "               INNER JOIN scenario_tree b\n" +
                    "                          on a.scenario_code = b.scenario_code\n" +
                    "      where a.scenario_code in \n" +
                    "<foreach item='item1' collection='list1' separator=',' open='(' close=')' index='index'>" +
                    "#{item1}" +
                    "</foreach>" +
                    ") tree\n" +
                    "         left join\n" +
                    "     (SELECT a.id                       scenarioId,\n" +
                    "             a.scenario_code            scenarioCode,\n" +
                    "             a.scenario_name            scenarioName,\n" +
                    "             a.trigger_types            triggerTypes,\n" +
                    "             a.state                    scenarioState,\n" +
                    "             a.publish_time             publishDate,\n" +
                    "             a.validity_start_date      validityStartDate,\n" +
                    "             a.validity_end_date        validityEndDate,\n" +
                    "             IFNULL(b.cal_amount, 0.00) calAmount,\n" +
                    "             IFNULL(b.price, 0.00)      scenarioPrice\n" +
                    "      FROM scenario_info a\n" +
                    "               left JOIN scenario_amount b ON a.scenario_code = b.scenario_code\n" +
                    "      WHERE a.state in ('1', '2') and a.trigger_types is not null \n" +
                    "        AND a.trigger_types is not null\n" +
                    "        AND (a.validity_end_date IS NULL OR\n" +
                    "             (a.validity_end_date >= CURRENT_TIMESTAMP AND CURRENT_TIMESTAMP >= a.validity_start_date))\n" +
                    "        AND a.scenario_code in \n" +
                    "<foreach item='item2' collection='list2' separator=',' open='(' close=')' index='index'>" +
                    "#{item2}" +
                    "</foreach>" +
                    "      order by scenarioId desc) scenarioInfo\n" +
                    "     on scenarioInfo.scenarioCode = tree.scenario_code\n" +
                    "     where scenarioInfo.scenarioCode is not null" +
                    "</script>"
    })
    @DS("business")
    public List<ScenarioInfoTreeBusiness> queryScenarioTreeBatch(@Param("list1") List<String> scenarioTreeCodes1,
                                                                 @Param("list2") List<String> scenarioTreeCodes2);


    /**
     * 查询所有场景等级信息
     * 
     * @return 场景等级信息列表
     */
    @Select("SELECT  scenario_code as scenarioCode,scenario_level as scenarioLevel from scenario_info WHERE scenario_code IS NOT NULL and scenario_level is not null")
    @DS("business")
    List<ScenarioLevel> queryScenarioLevel();

}
