package com.beantechs.bigdata.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.service.utils.CommonBeanUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("scenario_tree_info")
public class ScenarioInfo implements Serializable {
    /**
     * Base64编码：vin_treeCode_scenarioCode编码
     */
    @TableId
    private String id;
    /**
     * 剧情树名称
     */
    @TableField("tree_name")
    private String treeName;
    /**
     * 剧情树Code
     */
    @TableField("tree_code")
    private String treeCode;
    /**
     * vin
     */
    @TableField("vin")
    private String vin;

    /**
     * 剧本id
     */
    @TableField("scenario_id")
    private Integer scenarioId;
    /**
     * 剧本code
     */
    @TableField("scenario_code")
    private String scenarioCode;
    /**
     * 剧本code
     */
    @TableField("scenario_name")
    private String scenarioName;
    /**
     * 触发器类型
     */
    @TableField("trigger_types")
    private String triggerTypes;
    /**
     * 触发器数量
     */
    @TableField("trigger_num")
    private Integer triggerNum;
    /**
     * 剧本状态
     */
    @TableField("scenario_state")
    private Integer scenarioState;
    /**
     * 发布日期
     */
    @TableField("publish_date")
    private Date publishDate;
    /**
     * 生效日期
     */
    @TableField("validity_start_date")
    private Date validityStartDate;
    /**
     * 失效日期
     */
    @TableField("validity_end_date")
    private Date validityEndDate;
    /**
     * 竞价金额
     */
    @TableField("cal_amount")
    private double calAmount;
    /**
     * 剧本单价
     */
    @TableField("scenario_price")
    private BigDecimal scenarioPrice;

    @TableField("update_time")
    private String updateTime = CommonBeanUtils.dateTime2String(new Date());


    public static ScenarioInfo spiltTriggerTypes(ScenarioInfo scenarioInfo) {
        scenarioInfo.setTriggerNum(scenarioInfo.getTriggerTypes().split(ScenarioConstants.JAVA_COMMON_SPLIT_STR).length);
        return scenarioInfo;
    }
}
