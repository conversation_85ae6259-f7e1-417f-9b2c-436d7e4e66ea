package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beantechs.bigdata.service.entity.ScenarioPkInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioPkTreeInfoMapper extends BaseMapper<ScenarioPkInfo> {

    /**
     * 批量替换场景树PK结果信息
     * 
     * @param scenarioPkInfoList 场景PK信息列表
     */
    @Insert("<script>" +
            "REPLACE INTO scenario_tree_pk_result_info_di (id, vin, tree_code, tree_name, scenario_id, scenario_code, scenario_name, scenario_publish_days, scenario_publish_credit, scenario_max_price, cal_amount, scenario_price, scenario_update_score, scenario_initial_credit, scenario_price_credit, scenario_total_credit, scenario_dest_credit, trigger_num, scenario_state, publish_date, validity_start_date, validity_end_date, cal_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.vin}, #{item.treeCode}, #{item.treeName}, #{item.scenarioId}, #{item.scenarioCode}, #{item.scenarioName}, #{item.scenarioPublishDays}, #{item.scenarioPublishCredit}, #{item.scenarioMaxPrice}, #{item.calAmount}, #{item.scenarioPrice}, #{item.scenarioUpdateScore}, #{item.scenarioInitialCredit}, #{item.scenarioPriceCredit}, #{item.scenarioTotalCredit}, #{item.scenarioDestCredit}, #{item.triggerNum}, #{item.scenarioState}, #{item.publishDate}, #{item.validityStartDate}, #{item.validityEndDate}, #{item.calTime})" +
            "</foreach>" +
            "</script>")
    void replaceBatch(@Param("list") List<ScenarioPkInfo> scenarioPkInfoList);

}
