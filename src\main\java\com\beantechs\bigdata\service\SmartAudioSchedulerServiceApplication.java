package com.beantechs.bigdata.service;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableSwagger2
@EnableApolloConfig
@EnableAsync
@EnableScheduling
@Slf4j
@MapperScan(basePackages = "com.beantechs.bigdata.service.mapper.*")
public class SmartAudioSchedulerServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmartAudioSchedulerServiceApplication.class, args);
        log.warn("==================================project run success=========================================");
    }
}
