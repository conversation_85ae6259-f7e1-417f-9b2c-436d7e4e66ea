package com.beantechs.bigdata.service.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.service.utils.CommonBeanUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

import static java.lang.Math.min;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@TableName("scenario_tree_pk_result_info_di")
public class ScenarioPkInfo implements Serializable {

    @TableId
    private String id;

    @TableField("vin")
    private String vin;

    @TableField("tree_code")
    private String treeCode;

    @TableField("tree_name")
    private String treeName;

    /**
     * 剧本id
     */
    @TableField("scenario_id")
    private Integer scenarioId;
    /**
     * 剧本code
     */
    @TableField("scenario_code")
    private String scenarioCode;
    /**
     * 剧本code
     */
    @TableField("scenario_name")
    private String scenarioName;
    /**
     * 剧本上线天数
     */
    @TableField("scenario_publish_days")
    private Integer scenarioPublishDays;
    /**
     * 剧本发布时间决定的信用值
     */
    @TableField("scenario_publish_credit")
    private double scenarioPublishCredit;
    /**
     * 剧本中最大竞价金额
     */
    @TableField("scenario_max_price")
    private double scenarioMaxPrice;
    /**
     * 竞价金额
     */
    @TableField("cal_amount")
    private double calAmount;
    /**
     * 剧本单价
     */
    @TableField("scenario_price")
    private BigDecimal scenarioPrice;
    /**
     * 剧本单价归一化结果分数
     */
    @TableField("scenario_update_score")
    private BigDecimal scenarioUpdateScore;
    /**
     * 剧本单价决定的信用值
     */
    @TableField("scenario_initial_credit")
    private BigDecimal scenarioInitialCredit;
    /**
     * 剧本单价决定的信用值
     */
    @TableField("scenario_price_credit")
    private BigDecimal scenarioPriceCredit;
    /**
     * 剧本信用值
     */
    @TableField("scenario_total_credit")
    private BigDecimal scenarioTotalCredit;
    /**
     * 剧本最终信用值，根据正态分布取随机
     */
    @TableField("scenario_dest_credit")
    private BigDecimal scenarioDestCredit;
    /**
     * 剧本信用值-加上均值
     */
    @TableField(exist = false)
    private BigDecimal scenarioTotalWithAvgCredit;
    /**
     * 剧本最终信用值，根据正态分布取随机
     */
    @TableField(exist = false)
    private BigDecimal scenarioDestWithAvgCredit;
    /**
     * 触发器数量
     */
    @TableField("trigger_num")
    private Integer triggerNum;
    /**
     * 剧本状态
     */
    @TableField("scenario_state")
    private Integer scenarioState;
    /**
     * 发布日期
     */
    @TableField("publish_date")
    private Date publishDate;
    /**
     * 生效日期
     */
    @TableField("validity_start_date")
    private Date validityStartDate;
    /**
     * 失效日期
     */
    @TableField("validity_end_date")
    private Date validityEndDate;


    @TableField("cal_time")
    private String calTime = CommonBeanUtils.dateTime2String(new Date());

    /**
     * @param scenarioInfo                       剧本基础信息
     * @param dayCreditWeight                    权重系数
     * @param scoreCreditWeight                  权重系数
     * @param scenarioPriceUpdatedWeightMultiply 权重系数
     * @param scenarioPriceUpdatedWeightAdd      权重系数
     * @param normalPriceWeight                  权重系数
     * @param normalWeight                       权重系数
     * @param currentDateTimeStr                 当天时间字符串
     * @param maxAmount                          当前剧本列表最大金额
     * @return 剧本竞价信息
     */
    public static ScenarioPkInfo of(
            ScenarioInfo scenarioInfo,
            BigDecimal dayCreditWeight,
            BigDecimal scoreCreditWeight,
            BigDecimal scenarioPriceUpdatedWeightMultiply,
            BigDecimal scenarioPriceUpdatedWeightAdd,
            BigDecimal normalPriceWeight,
            BigDecimal normalWeight,
            String currentDateTimeStr,
            Double maxAmount
    ) {
        ScenarioPkInfo scenarioPkInfo = new ScenarioPkInfo();
        //剧本id
        scenarioPkInfo.setScenarioId(scenarioInfo.getScenarioId());
        scenarioPkInfo.setScenarioCode(scenarioInfo.getScenarioCode());
        scenarioPkInfo.setScenarioName(scenarioInfo.getScenarioName());
        //上线天数
        final int dateNum = calculateDateNum(scenarioInfo, currentDateTimeStr);

        scenarioPkInfo.setScenarioPublishDays(dateNum);
        //计算上线天数决定的剧本信用值
        final double sqrtDest = calculateCreditByDateNum(dayCreditWeight, scoreCreditWeight, dateNum);

        scenarioPkInfo.setScenarioPublishCredit(sqrtDest);
        //最大竞价金额
        scenarioPkInfo.setScenarioMaxPrice(maxAmount);
        //剧本竞价金额
        final double amount = new BigDecimal(0).compareTo(BigDecimal.valueOf(scenarioInfo.getCalAmount())) > 0 ? 0 : scenarioInfo.getCalAmount();
        scenarioPkInfo.setCalAmount(amount);
        //剧本单价
        scenarioPkInfo.setScenarioPrice(scenarioInfo.getScenarioPrice());
        //将所有剧本的竞价金额归一化到[0,1]分数
        BigDecimal calAmount = BigDecimal.valueOf(amount);
        BigDecimal maxAmountBig = BigDecimal.valueOf(maxAmount);
        final BigDecimal scenarioPriceUpdated = 0 != maxAmountBig.doubleValue() ? calAmount.divide(maxAmountBig, 3, RoundingMode.HALF_UP) : new BigDecimal(0);

        scenarioPkInfo.setScenarioUpdateScore(scenarioPriceUpdated);
        //计算竞价金额决定的剧本信用值
        final BigDecimal pMoneyInitial = scenarioPriceUpdatedWeightMultiply.multiply(scenarioPriceUpdated).add(scenarioPriceUpdatedWeightAdd).setScale(3, RoundingMode.HALF_UP);

        scenarioPkInfo.setScenarioInitialCredit(pMoneyInitial);
        //计算权重系数计算的分数
        final BigDecimal pMoney = pMoneyInitial.multiply(normalPriceWeight).setScale(3, RoundingMode.HALF_UP);
        scenarioPkInfo.setScenarioPriceCredit(pMoney);
        //剧本信用值
        final BigDecimal pTotal = pMoney.add(BigDecimal.valueOf(sqrtDest)).setScale(3, RoundingMode.HALF_UP);
        scenarioPkInfo.setScenarioTotalCredit(pTotal);
        //随机扰动重新输出剧本信用值
        BigDecimal disturbance = normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(pTotal).setScale(3, RoundingMode.HALF_UP);
        scenarioPkInfo.setScenarioDestCredit(disturbance);
        scenarioPkInfo.setTriggerNum(scenarioInfo.getTriggerNum());
        scenarioPkInfo.setScenarioState(scenarioInfo.getScenarioState());
        scenarioPkInfo.setPublishDate(scenarioInfo.getPublishDate());
        scenarioPkInfo.setValidityStartDate(scenarioInfo.getValidityStartDate());
        scenarioPkInfo.setValidityEndDate(scenarioInfo.getValidityEndDate());
        return scenarioPkInfo;
    }

    /**
     * 计算上线天数决定的剧本信用值
     *
     * @param dayCreditWeight   权重系数
     * @param scoreCreditWeight 权重系数
     * @param dateNum           上线日期
     * @return 剧本竞价日期值
     */
    private static double calculateCreditByDateNum(BigDecimal dayCreditWeight, BigDecimal scoreCreditWeight, int dateNum) {
        final int x = Math.max(0, dateNum);
        final BigDecimal a2 = dayCreditWeight.multiply(dayCreditWeight);
        final BigDecimal b2 = scoreCreditWeight.multiply(scoreCreditWeight);
        final BigDecimal bigDecimal = 0 != a2.doubleValue() ? a2.multiply(b2).subtract(b2.multiply(new BigDecimal(x * x))).divide(a2, 3, RoundingMode.HALF_UP) : new BigDecimal(0);
        final double sqrt = Math.sqrt(bigDecimal.doubleValue());
        return min(sqrt, 0.4) + 0.6;
    }

    /**
     * 计算上线天数
     *
     * @param scenarioInfo       剧本竞价信息
     * @param currentDateTimeStr 当天日期字符串
     * @return 上线天数
     */
    private static int calculateDateNum(ScenarioInfo scenarioInfo, String currentDateTimeStr) {
        final Date publishDate = scenarioInfo.getPublishDate();
        final Date validityStartDate = scenarioInfo.getValidityStartDate();
        if (null == publishDate && null == validityStartDate) {
            return ScenarioConstants.NUMBER_NIGHTY;
        }
        String publishDateStr = CommonBeanUtils.date2String(publishDate);
        String validityStartDateStr = null == validityStartDate ? null : CommonBeanUtils.date2String(validityStartDate);
        int compareNum = 0;
        if (null != validityStartDate) {
            if (null == publishDate) {
                compareNum = 1;
            } else {
                compareNum = publishDate.compareTo(validityStartDate);
            }
        }
        int dateNum;
        try {
            if (null != validityStartDate) {
                dateNum = compareNum < 0 ?
                        Math.abs(getDaySub(publishDateStr + ScenarioConstants.TIME_FULL_ZERO_START, currentDateTimeStr)) :
                        Math.abs(getDaySub(validityStartDateStr + ScenarioConstants.TIME_FULL_ZERO_START, currentDateTimeStr));
            } else {
                dateNum = Math.abs(getDaySub(publishDateStr + ScenarioConstants.TIME_FULL_ZERO_START, currentDateTimeStr));
            }
        } catch (Exception e) {
            dateNum = ScenarioConstants.NUMBER_NIGHTY;
        }
        if (dateNum > ScenarioConstants.NUMBER_NIGHTY) {
            dateNum = ScenarioConstants.NUMBER_NIGHTY;
        }
        return dateNum;
    }


    /**
     * @param beginDateStr 开始时间
     * @param endDateStr   结束时间
     * @return 获取天数差
     */
    private static Integer getDaySub(String beginDateStr, String endDateStr) {
        long day = 0;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date beginDate;
        Date endDate;
        try {
            beginDate = format.parse(beginDateStr);
            endDate = format.parse(endDateStr);
            day = (endDate.getTime() - beginDate.getTime()) / (24 * 60 * 60 * 1000);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        return (int) day;
    }
}
