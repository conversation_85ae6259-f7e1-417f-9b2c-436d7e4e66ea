spring:
  profiles:
    active: qa
  application:
    name: smart-audio-service
app:
  id: smart-audio-service
apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则（camel case）映射，即从经典数据库列名 A_COLUMN（下划线命名）
    #到经典 Java 属性名 aColumn（驼峰命名） 的类似映射
    map-underscore-to-camel-case: true
logging:
  level:
    root: warn
management:
  endpoints:
    web:
      exposure:
        include: "*"