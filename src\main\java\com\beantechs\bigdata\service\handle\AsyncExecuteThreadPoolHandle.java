package com.beantechs.bigdata.service.handle;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioInfo;
import com.beantechs.bigdata.service.entity.ScenarioPkInfo;
import com.beantechs.bigdata.service.entity.ScenarioTreeKafkaDTO;
import com.beantechs.bigdata.service.entity.business.ScenarioLevel;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import com.beantechs.bigdata.service.mapper.*;
import com.beantechs.service.utils.FeiShuRobotUtils;
import com.beantechs.service.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.JedisPool;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Component
@Slf4j
@EnableAsync
public class AsyncExecuteThreadPoolHandle {

    private final ScenarioTreeInfoImpl scenarioTreeInfo;

    private final ScenarioTreeInfoMapper scenarioTreeInfoMapper;

    private final ScenarioPkTreeInfoMapper scenarioPKTreeInfoMapper;

    private final ScenarioPkTreeTriggerInfoMapper scenarioPKTreeTriggerInfoMapper;


    private final ScenarioPkTreeInfoImpl scenarioPKTreeInfo;


    private final ScenarioTagInfoBusinessImpl scenarioTagInfoBusinessImpl;


    private final ScenarioTagInfoDataserviceMapper scenarioTagInfoDataserviceMapper;


    private final ScenarioBusinessMapper scenarioBusinessMapper;

    @Value("${spring.application.name}")
    private String application;


    @Value("${environment}")
    private String environment;

    @Value("${feishu.warn.robot.url}")
    private String warnUrl;


    private final JedisPool jedisPool;

    /**
     * redis库:8
     */
    @Value("${redis.database}")
    private Integer indexDB;

    public AsyncExecuteThreadPoolHandle(ScenarioTreeInfoImpl scenarioTreeInfo, ScenarioTreeInfoMapper scenarioTreeInfoMapper, ScenarioPkTreeInfoMapper scenarioPKTreeInfoMapper, ScenarioPkTreeTriggerInfoMapper scenarioPKTreeTriggerInfoMapper, ScenarioPkTreeInfoImpl scenarioPKTreeInfo, ScenarioTagInfoBusinessImpl scenarioTagInfoBusinessImpl, ScenarioTagInfoDataserviceMapper scenarioTagInfoDataserviceMapper, ScenarioBusinessMapper scenarioBusinessMapper, JedisPool jedisPool) {
        this.scenarioTreeInfo = scenarioTreeInfo;
        this.scenarioTreeInfoMapper = scenarioTreeInfoMapper;
        this.scenarioPKTreeInfoMapper = scenarioPKTreeInfoMapper;
        this.scenarioPKTreeTriggerInfoMapper = scenarioPKTreeTriggerInfoMapper;
        this.scenarioPKTreeInfo = scenarioPKTreeInfo;
        this.scenarioTagInfoBusinessImpl = scenarioTagInfoBusinessImpl;
        this.scenarioTagInfoDataserviceMapper = scenarioTagInfoDataserviceMapper;
        this.scenarioBusinessMapper = scenarioBusinessMapper;
        this.jedisPool = jedisPool;
    }


    @Async
    public void saveScenarioTree2MySQL(List<ScenarioInfo> scenarioInfoList, List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) throws RuntimeException {
        log.warn("异步数据保存任务");
        long start = System.currentTimeMillis();
        try {
            scenarioTreeInfoMapper.replaceBatch(scenarioInfoList);
            scenarioPKTreeInfoMapper.replaceBatch(scenarioPkInfoList);
            scenarioPKTreeTriggerInfoMapper.replaceBatch(scenarioList);
        } catch (Exception e) {
            log.error("异步数据保存剧情树信用分结果任务失败, 失败原因:{}", e.getMessage());
            throw new RuntimeException(e);
        }
        long end = System.currentTimeMillis();
        log.warn("异步数据保存任务结束, 总耗时:{}ms", end - start);
    }


    @Async
    public void sendFeiShuRobotMsg(String msg, String cause) {
        FeiShuRobotUtils.WarnEntity warnEntity = new FeiShuRobotUtils.WarnEntity(application, environment, msg, cause, "骆云龙");
        FeiShuRobotUtils.sendTextMsg(warnUrl, warnEntity.toString());
    }


    @Async
    public void updateMaxAmountGroupVin(ScenarioTreeKafkaDTO scenarioTreeKafkaDTOS) {
        ScenarioPkInfo scenarioPKInfo = new ScenarioPkInfo();
        scenarioPKInfo.setScenarioMaxPrice(TypeUtils.castToDouble(scenarioTreeInfo.getMap(
                        new QueryWrapper<ScenarioInfo>()
                                .eq("vin", scenarioTreeKafkaDTOS.getVin())
                                .select("IFNULL( max(cal_amount),0) as amount"))
                .get("amount")));
        try {
            UpdateWrapper<ScenarioPkInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("vin", scenarioTreeKafkaDTOS.getVin()).set("cal_amount", scenarioPKInfo.getScenarioMaxPrice());
            scenarioPKTreeInfo.update(scenarioPKInfo, updateWrapper);
        } catch (Exception e) {
            log.warn("更新剧本金额信息:{}, maxamount:{}", JSON.toJSONString(scenarioTreeKafkaDTOS), scenarioPKInfo.getScenarioMaxPrice());
        }
    }


    public void syncScenarioTagInfo() {
        try {
            List<ScenarioTagInfo> list = scenarioTagInfoBusinessImpl.list();
            Integer i = scenarioTagInfoDataserviceMapper.replaceBatch(list);
            log.warn("批量更新剧本标签信息结果:{}, 当前时间::{}", i > 0 ? "成功" : "失败", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MS_FORMATTER));
        } catch (Exception e) {
            log.error("批量更新剧本标签信息异常:{}", e.getMessage(), e);
        }
    }


    public void syncScenarioLevel() {
        try {
            List<ScenarioLevel> scenarioLevels = scenarioBusinessMapper.queryScenarioLevel();
            if (!CollectionUtils.isEmpty(scenarioLevels)) {
                Map<String, String> collect = scenarioLevels.stream().collect(Collectors.toMap(ScenarioLevel::getScenarioCode, ScenarioLevel::getScenarioLevel));
                RedisUtils.hmset("SMART_AUDIO:SCENARIO:LEVEL:INFO", collect, indexDB, jedisPool);
            }
        } catch (Exception e) {
            log.error("更新剧本等级失败:" + e.getMessage(), e);
            FeiShuRobotUtils.sendTextMsg(warnUrl, "更新剧本等级失败:" + e.getMessage());
        }
    }


}
