package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beantechs.bigdata.service.entity.Scenario;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioPkTreeTriggerInfoMapper extends BaseMapper<Scenario> {

    /**
     * 批量替换场景树触发类型评分信息
     * 
     * @param scenarioList 场景信息列表
     */
    @Insert("<script>" +
            "REPLACE INTO scenario_tree_trigger_type_score_di (id, vin, tree_code, tree_name, scenario_id, scenario_code, scenario_name, trigger_type, trigger_num, scenario_total_credit, scenario_dest_credit, scenario_total_with_avg_credit, scenario_dest_with_avg_credit, cal_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.vin}, #{item.treeCode}, #{item.treeName}, #{item.scenarioId}, #{item.scenarioCode}, #{item.scenarioName}, #{item.triggerType}, #{item.triggerNum}, #{item.scenarioTotalCredit}, #{item.scenarioDestCredit}, #{item.scenarioTotalWithAvgCredit}, #{item.scenarioDestWithAvgCredit}, #{item.calTime})" +
            "</foreach>" +
            "</script>")
    void replaceBatch(@Param("list") List<Scenario> scenarioList);

}
