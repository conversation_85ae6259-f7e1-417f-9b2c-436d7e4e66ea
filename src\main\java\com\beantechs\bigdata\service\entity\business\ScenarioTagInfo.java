package com.beantechs.bigdata.service.entity.business;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@TableName("scenario_tag_info")
public class ScenarioTagInfo {

    @TableId("id")
    private Long id;

    @TableField("scenario_code")
    private String scenarioCode;

    @TableField("tag_code")
    private String tagCode;

    @TableField("create_by")
    private String createBy;

    @TableField("create_time")
    private String createTime;

    @TableField("update_by")
    private String updateBy;

    @TableField("update_time")
    private String updateTime;

    @TableField("is_delete")
    private Integer isDelete;
}