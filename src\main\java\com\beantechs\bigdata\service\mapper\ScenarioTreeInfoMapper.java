package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beantechs.bigdata.service.entity.ScenarioInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioTreeInfoMapper extends BaseMapper<ScenarioInfo> {


    /**
     * 批量替换场景树信息
     * 
     * @param scenarioInfoList 场景信息列表
     */
    @Insert("<script>" +
            "REPLACE INTO scenario_tree_info (id, tree_name, tree_code, vin, scenario_id, scenario_code, scenario_name, trigger_types, trigger_num, scenario_state, publish_date, validity_start_date, validity_end_date, cal_amount, scenario_price, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.treeName}, #{item.treeCode}, #{item.vin}, #{item.scenarioId}, #{item.scenarioCode}, #{item.scenarioName}, #{item.triggerTypes}, #{item.triggerNum}, #{item.scenarioState}, #{item.publishDate}, #{item.validityStartDate}, #{item.validityEndDate}, #{item.calAmount}, #{item.scenarioPrice}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    void replaceBatch(@Param("list") List<ScenarioInfo> scenarioInfoList);

}
