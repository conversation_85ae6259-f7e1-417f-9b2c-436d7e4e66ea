package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;
/**
 * <AUTHOR>
 */
@Mapper
@DS("slave")
public interface ScenarioBigdataSlaveMapper {

    /**
     * 根据场景代码查询标签信息
     *
     * @param scenarioCodeSet 场景代码集合
     * @return 场景标签信息列表
     */
    @Select({
            "<script>",
            "SELECT * FROM scenario_tag_info WHERE scenario_code IN",
            "<foreach collection='list' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    @DS("slave")
    List<ScenarioTagInfo> queryTagByScenarioCode(@Param("list") Set<String> scenarioCodeSet);

    /**
     * 查询场景树信息总数
     *
     * @return 场景树信息总数
     */
    @Select({"<script>",
            "select count(0) from scenario_tree_info",
            "</script>"
    })
    @DS("slave")
    Integer queryScenarioTreeInfoCount();

    /**
     * 批量查询场景树信息
     *
     * @param index 起始索引
     * @return 场景信息列表
     */
    @Select({"<script>",
            "select * from scenario_tree_info where id >= (select id from scenario_tree_info limit ${num},1) limit 10000",
            "</script>"
    })
    @DS("slave")
    List<ScenarioInfo> queryScenarioTreeInfoBatch(@Param("num") Integer index);


}
