package com.beantechs.bigdata.service.config;

import com.alibaba.fastjson.JSON;
import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.business.ScenarioInfoTreeBusiness;
import com.beantechs.bigdata.service.handle.AsyncExecuteThreadPoolHandle;
import com.beantechs.bigdata.service.handle.CalculateHandle;
import com.beantechs.bigdata.service.mapper.*;
import com.beantechs.service.utils.CommonBeanUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class KafkaConsumer {

    private final CalculateHandle calculateHandle;

    private final ScenarioBusinessMapper scenarioBusinessMapper;

    private final ScenarioBigdataMapper scenarioBigdataMapper;

    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    private final ScenarioPkTreeInfoImpl scenarioPKTreeInfo;

    private final ScenarioTreeTriggerInfoImpl scenarioPKTreeTriggerInfo;

    private final ScenarioBigdataSlaveMapper bigdataSlaveMapper;

    public KafkaConsumer(CalculateHandle calculateHandle, AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle, ScenarioPkTreeInfoImpl scenarioPKTreeInfo, ScenarioTreeTriggerInfoImpl scenarioPKTreeTriggerInfo, ScenarioBusinessMapper scenarioBusinessMapper, ScenarioBigdataMapper scenarioBigdataMapper, ScenarioBigdataSlaveMapper bigdataSlaveMapper) {
        this.calculateHandle = calculateHandle;
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
        this.scenarioPKTreeInfo = scenarioPKTreeInfo;
        this.scenarioPKTreeTriggerInfo = scenarioPKTreeTriggerInfo;
        this.scenarioBusinessMapper = scenarioBusinessMapper;
        this.scenarioBigdataMapper = scenarioBigdataMapper;
        this.bigdataSlaveMapper = bigdataSlaveMapper;
    }


    @KafkaListener(topics = "${smart.audio.business.pull.kafka.signal}", groupId = "smart-audio-service-consumer-1-4")
    public void consumer(ConsumerRecord<String, String> consumerRecord) {
        Optional.ofNullable(consumerRecord.value()).ifPresent(str -> log.warn(Boolean.TRUE.equals(calculateHandle.queryAndCalculateScenario()) ?
                "剧本更新事件..." :
                "calculate scenario failed! current time:{}", CommonBeanUtils.dateTime2String(new Date())));
    }

    @KafkaListener(topics = "${smart.audio.pk.result.handle.produce.topic}", groupId = "smart-audio-service-consumer-2-4")
    public void consumerQueryRecord(ConsumerRecord<String, String> consumerRecord) {
        Optional.ofNullable(consumerRecord.value()).ifPresent(o -> save2MySQL(JSON.parseArray(o, ScenarioQueryRecord.class)));
    }

    /**
     * 剧情树增量更新信息
     */
    @KafkaListener(topics = "${smart.audio.scenario.tree.update.event}", groupId = "smart-audio-service-consumer-3-4")
    public void consumerScenarioTreeUpdate(ConsumerRecord<String, String> consumerRecord) {
        ScenarioTreeKafkaDTO scenarioTreeKafkaDTOS = JSON.parseObject(consumerRecord.value(), ScenarioTreeKafkaDTO.class);
        log.warn("剧情树更新事件, data:{}", JSON.toJSONString(scenarioTreeKafkaDTOS));
        //查询剧情树信息
        StopWatch stopWatch = new StopWatch("kafka 消费剧情树更新信息");
        stopWatch.start("根据kafka剧情树code查询业务库剧本信息");
        List<ScenarioInfo> scenarioInfoList = scenarioBusinessMapper.queryScenarioTreeBatch(
                        scenarioTreeKafkaDTOS.getScenarioCodes(),
                        scenarioTreeKafkaDTOS.getScenarioCodes()
                )
                .stream()
                .map(item -> setTreeFields(item, scenarioTreeKafkaDTOS))
                .collect(Collectors.toList());
        log.warn("查询剧情树基本信息:{}", JSON.toJSONString(scenarioInfoList));
        stopWatch.stop();
        if (!CollectionUtils.isEmpty(scenarioInfoList)) {
            stopWatch.start("计算信用分");
            calScoreAndSaveMySQL(stopWatch, scenarioInfoList);
            stopWatch.stop();
            asyncExecuteThreadPoolHandle.updateMaxAmountGroupVin(scenarioTreeKafkaDTOS);
            log.warn(stopWatch.prettyPrint());
        }
    }


    @NotNull
    private static ScenarioInfo setTreeFields(ScenarioInfoTreeBusiness scenarioInfoTreeBusiness, ScenarioTreeKafkaDTO scenarioTreeKafkaDTOS) {
        ScenarioInfo scenarioInfo = new ScenarioInfo();
        BeanUtils.copyProperties(scenarioInfoTreeBusiness, scenarioInfo);
        scenarioInfo.setVin(scenarioTreeKafkaDTOS.getVin());
        scenarioInfo.setPublishDate(CommonBeanUtils.dateStrFormatterToDateHMS(scenarioTreeKafkaDTOS.getTime()));
        scenarioInfo.setId(scenarioTreeKafkaDTOS.getVin() + "_" + scenarioInfo.getTreeCode() + "_" + scenarioInfo.getScenarioCode());
        return ScenarioInfo.spiltTriggerTypes(scenarioInfo);
    }


    private void calScoreAndSaveMySQL(StopWatch stopWatch, List<ScenarioInfo> scenarioInfoList) {
        try {
            //PK信用分
            List<ScenarioPkInfo> scenarioPkInfoList = calculateHandle.calScenario(scenarioInfoList);
            //拓展触发器信用分
            List<Scenario> scenarioList = calculateHandle.calScenarioTriggerInfoList(scenarioInfoList, scenarioPkInfoList);
            stopWatch.stop();
            stopWatch.start("保存计算结果");
            asyncExecuteThreadPoolHandle.saveScenarioTree2MySQL(scenarioInfoList, scenarioPkInfoList, scenarioList);
        } catch (Exception e) {
            log.error("保存剧情树信息至MySQL异常, 原因:{}", e.getMessage());
            asyncExecuteThreadPoolHandle.sendFeiShuRobotMsg("保存剧情树信息至MySQL异常", e.getMessage());
        }
    }

    public void refreshScores() throws Exception {
        StopWatch stopWatch = new StopWatch("刷新剧情树信用分task");
        stopWatch.start("分页查询MySQL剧情树信息");
        List<ScenarioInfo> scenarioInfoArrayList = queryMySQLScenarioData(stopWatch);
        stopWatch.stop();
        scenarioInfoArrayList = scenarioInfoArrayList.stream().filter(item -> StringUtils.isNotBlank(item.getScenarioCode())).collect(Collectors.toList());
        //PK信用分
        stopWatch.start("计算PK信用分");
        List<ScenarioPkInfo> scenarioPkInfoList = calculateHandle.calScenario(scenarioInfoArrayList);
        stopWatch.stop();
        //拓展触发器信用分
        stopWatch.start("计算PK触发器信用分");
        List<Scenario> scenarioList = calculateHandle.calScenarioTriggerInfoList(scenarioInfoArrayList, scenarioPkInfoList);
        stopWatch.stop();
        stopWatch.start("刷新保存信号分");
        cacheHandle(scenarioPkInfoList, scenarioList);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
    }

    @NotNull
    private ArrayList<ScenarioInfo> queryMySQLScenarioData(StopWatch stopWatch) throws Exception {
        long count = bigdataSlaveMapper.queryScenarioTreeInfoCount();
        long page = count / 10000;
        if (count % 10000 == 0) {
            page++;
        }
        ArrayList<ScenarioInfo> scenarioInfoArrayList = Lists.newArrayList();
        try {
            for (int i = 0; i <= page; i++) {
                scenarioInfoArrayList.addAll(bigdataSlaveMapper.queryScenarioTreeInfoBatch(i * 10000));
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.warn(stopWatch.prettyPrint());
            throw new Exception(e);
        }
        return scenarioInfoArrayList;
    }

    private void cacheHandle(List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) {
        scenarioPKTreeInfo.saveOrUpdateBatch(scenarioPkInfoList);
        scenarioPKTreeTriggerInfo.saveOrUpdateBatch(scenarioList);
    }


    public void save2MySQL(List<ScenarioQueryRecord> recordList) {
        final String dateStr = CommonBeanUtils.dateTime2String(new Date());
        try {
            List<ScenarioStatisticsNum> collect = recordList.stream().map(obj -> scenarioStatisticsNumBuild(dateStr, obj)).collect(Collectors.toList());
            scenarioBigdataMapper.saveScenarioCallNumBatch(collect);
        } catch (Exception e) {
            log.error("save scenario call num batch failed! error msg:{}", e.getCause().getMessage());
        }
    }

    @NotNull
    private static ScenarioStatisticsNum scenarioStatisticsNumBuild(String dateStr, ScenarioQueryRecord obj) {
        ScenarioStatisticsNum scenarioStatisticsNum = new ScenarioStatisticsNum();
        scenarioStatisticsNum.setActionSign(obj.getActionSign());
        scenarioStatisticsNum.setCallTime(dateStr);
        scenarioStatisticsNum.setScenarioCode(obj.getScenarioCode());
        scenarioStatisticsNum.setScenarioName(obj.getScenarioName());
        scenarioStatisticsNum.setVin(obj.getVin());
        scenarioStatisticsNum.setBeanId(obj.getBeanId());
        scenarioStatisticsNum.setFeedbackCredit(obj.getFeedbackCredit());
        scenarioStatisticsNum.setScenarioTotalCredit(obj.getScenarioTotalCredit());
        scenarioStatisticsNum.setScenarioDestCredit(obj.getScenarioDestCredit());
        scenarioStatisticsNum.setCallApiParamCodes(obj.getCodes());
        scenarioStatisticsNum.setSuccessNum(obj.isStatus() ? 1 : 0);
        scenarioStatisticsNum.setCallNum(1);
        scenarioStatisticsNum.setApiVersion(obj.getApiVersion());
        return scenarioStatisticsNum;
    }

}
