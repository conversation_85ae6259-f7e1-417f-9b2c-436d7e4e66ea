package com.beantechs.bigdata.service.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioPkInfo;
import com.beantechs.bigdata.service.entity.ScenarioStatisticsNum;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS("dataservice")
public interface ScenarioBigdataMapper {
    /**
     * 保存场景PK信息列表
     * 
     * @param scenarioPkInfoList 场景PK信息列表
     * @param date 计算时间
     * @return 保存的记录数
     */
    @Insert({"<script>",
            "Insert into scenario_pk_result_info ",
            "(scenario_id,scenario_code,scenario_name,scenario_publish_days,scenario_publish_credit,scenario_max_price,cal_amount,scenario_price,",
            "scenario_update_score,scenario_initial_credit,scenario_price_credit,scenario_total_credit,scenario_dest_credit,",
            "trigger_num,scenario_state,publish_date,validity_start_date,validity_end_date,cal_time) values ",
            "<foreach item='item' collection='list' separator=',' index='index'>",
            "(#{item.scenarioId},#{item.scenarioCode},#{item.scenarioName},#{item.scenarioPublishDays},#{item.scenarioPublishCredit},#{item.scenarioMaxPrice},",
            "#{item.calAmount},#{item.scenarioPrice},#{item.scenarioUpdateScore},#{item.scenarioInitialCredit},",
            "#{item.scenarioPriceCredit},#{item.scenarioTotalCredit},#{item.scenarioDestCredit},#{item.triggerNum},",
            "#{item.scenarioState},#{item.publishDate},#{item.validityStartDate},#{item.validityEndDate},#{date})",
            "</foreach>",
            "</script>"
    })
    @DS("dataservice")
    public Integer saveScenarioPkInfos(@Param("list") List<ScenarioPkInfo> scenarioPkInfoList,
                                       @Param("date") Date date);

    /**
     * 保存场景触发类型评分信息
     *
     * @param scenarioList 场景列表
     * @param date 计算时间
     * @return 保存的记录数
     */
    @Insert({"<script>",
            "Insert into scenario_trigger_type_score ",
            "(scenario_id,scenario_code,scenario_name,trigger_type,trigger_num,scenario_total_credit,scenario_dest_credit,scenario_total_with_avg_credit,scenario_dest_with_avg_credit,cal_time) values ",
            "<foreach item='item' collection='list' separator=',' index='index'>",
            "(#{item.scenarioId},#{item.scenarioCode},#{item.scenarioName},#{item.triggerType},#{item.triggerNum},#{item.scenarioTotalCredit},#{item.scenarioDestCredit},#{item.scenarioTotalWithAvgCredit},#{item.scenarioDestWithAvgCredit},",
            "#{date})",
            "</foreach>",
            "</script>"
    })
    @DS("dataservice")
    Integer saveScenarioTriggerTypeScore(@Param("list") List<Scenario> scenarioList,
                                         @Param("date") Date date);

    /**
     * 批量保存场景统计数量信息
     *
     * @param list 场景统计数量信息列表
     * @return 保存的记录数
     */
    @Insert({"<script>",
            "INSERT INTO scenario_statistics_num (action_sign,vin,bean_id,scenario_code,scenario_name,call_num,success_num,feedback_credit,scenario_total_credit,",
            "scenario_dest_credit,call_api_param_codes,call_time,api_version) VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.actionSign},#{item.vin},#{item.beanId},#{item.scenarioCode},#{item.scenarioName},${item.callNum},${item.successNum},",
            "#{item.feedbackCredit},#{item.scenarioTotalCredit},#{item.scenarioDestCredit},#{item.callApiParamCodes},#{item.callTime},#{item.apiVersion})",
            "</foreach>",
            "</script>"
    })
    @DS("dataservice")
    public Integer saveScenarioCallNumBatch(@Param("list") List<ScenarioStatisticsNum> list);


}
